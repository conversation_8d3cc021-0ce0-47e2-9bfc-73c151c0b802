<?php
require_once 'includes/header.php';
require_once 'includes/streaming_helper.php';
require_once 'includes/ad_placeholder.php';

// Add custom CSS for TV show details page
echo '<link rel="stylesheet" href="' . SITE_URL . '/css/tvshow-details.css">';

// Add custom CSS for movie cards
echo '<style>
    /* Movie Card Link Style */
    .movie-card-link {
        display: block;
        text-decoration: none;
        color: white;
        height: 100%;
    }

    .movie-card-link:hover {
        text-decoration: none;
        color: white;
    }

    /* Similar Shows Carousel Styles */
    .similar-shows-section {
        background-color: #0a0a0a;
        position: relative;
        overflow: hidden;
    }

    .section-header {
        margin-bottom: 20px;
    }

    .section-title {
        color: #fff;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0;
        position: relative;
        padding-left: 15px;
        border-left: 4px solid #ff7b00;
    }

    .carousel-controls {
        display: flex;
        gap: 10px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .carousel-control-prev:hover,
    .carousel-control-next:hover {
        background-color: #ff7b00;
    }

    .similar-shows-carousel {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 15px;
        overflow-x: auto;
        padding-bottom: 15px;
        scroll-behavior: smooth;
        scrollbar-width: thin;
        scrollbar-color: #ff7b00 #1a1a1a;
    }

    .similar-shows-carousel::-webkit-scrollbar {
        height: 6px;
    }

    .similar-shows-carousel::-webkit-scrollbar-track {
        background: #1a1a1a;
        border-radius: 10px;
    }

    .similar-shows-carousel::-webkit-scrollbar-thumb {
        background-color: #ff7b00;
        border-radius: 10px;
    }

    .similar-show-item {
        min-width: 160px;
    }

    .similar-show-link {
        text-decoration: none;
        color: #fff;
        display: block;
    }

    .similar-show-card {
        background-color: #1a1a1a;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .similar-show-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .similar-show-poster {
        position: relative;
        aspect-ratio: 2/3;
        overflow: hidden;
    }

    .similar-show-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .similar-show-card:hover .similar-show-poster img {
        transform: scale(1.05);
    }

    .premium-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        background-color: #ff7b00;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        z-index: 2;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .trailer-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 2;
    }

    .trailer-btn a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        transition: all 0.3s ease;
    }

    .trailer-btn a:hover {
        background-color: #ff7b00;
    }

    .similar-show-info {
        padding: 10px;
    }

    .similar-show-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .similar-show-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #aaa;
    }

    .similar-show-meta .rating {
        color: #ffc107;
    }

    /* Download and Watch Links Styles */
    .download-links-container {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .download-link {
        flex: 1;
        margin-right: 5px;
    }

    .watch-link {
        background: linear-gradient(135deg, #4a00e0, #8e2de2);
        color: white;
        text-decoration: none;
        border-radius: 4px;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .watch-link:hover {
        background: linear-gradient(135deg, #5a17f3, #9d45e2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        color: white;
    }

    .watch-button {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    /* Responsive styles */
    @media (max-width: 1199.98px) {
        .similar-shows-carousel {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    @media (max-width: 991.98px) {
        .similar-shows-carousel {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 767.98px) {
        .similar-shows-carousel {
            grid-template-columns: repeat(3, 1fr);
        }

        .similar-show-title {
            font-size: 0.85rem;
        }

        .similar-show-meta {
            font-size: 0.75rem;
        }

        .download-links-container {
            flex-direction: column;
            gap: 5px;
        }

        .download-link {
            margin-right: 0;
            margin-bottom: 5px;
            width: 100%;
        }

        .watch-link {
            width: 100%;
        }
    }

    @media (max-width: 575.98px) {
        .similar-shows-carousel {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .section-title {
            font-size: 1.2rem;
        }

        .similar-show-item {
            min-width: 140px;
        }

        .similar-show-info {
            padding: 8px;
        }

        .similar-show-title {
            font-size: 0.8rem;
        }

        .similar-show-meta {
            font-size: 0.7rem;
        }

        .premium-tag,
        .trailer-btn a {
            width: 20px;
            height: 20px;
            font-size: 0.6rem;
        }
    }

    /* Telegram Join Button Styles */
    .btn-telegram {
        background: linear-gradient(135deg, #0088cc, #0056a3);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 136, 204, 0.3);
    }

    .btn-telegram:hover {
        background: linear-gradient(135deg, #0099dd, #0067b4);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 136, 204, 0.4);
        color: white;
    }

    .btn-telegram i {
        font-size: 1.2rem;
    }
</style>';

// Check if id is provided
if (!isset($_GET['id'])) {
    redirect(SITE_URL);
}

$id = (int)$_GET['id'];

// Get TV show details
$query = "SELECT t.*, c.name as category_name, t.premium_only FROM tvshows t
          LEFT JOIN categories c ON t.category_id = c.id
          WHERE t.id = $id";

// Get all genres for the TV show
$genres_query = "SELECT c.name FROM tvshow_genres tg
               JOIN categories c ON tg.genre_id = c.id
               WHERE tg.tvshow_id = $id
               ORDER BY c.name";
$genres_result = mysqli_query($conn, $genres_query);
$result = mysqli_query($conn, $query);

// Check if TV show exists
if (mysqli_num_rows($result) == 0) {
    redirect(SITE_URL);
}

$tvshow = mysqli_fetch_assoc($result);

// Get reviews
$reviews_query = "SELECT r.*, u.username, u.profile_image FROM reviews r
                 JOIN users u ON r.user_id = u.id
                 WHERE r.content_type = 'tvshow' AND r.content_id = $id
                 ORDER BY r.created_at DESC";
$reviews_result = mysqli_query($conn, $reviews_query);

// Check if TV show is in user's watchlist
$in_watchlist = false;
if (isLoggedIn()) {
    $watchlist_query = "SELECT id FROM watchlist
                       WHERE user_id = {$_SESSION['user_id']}
                       AND content_type = 'tvshow'
                       AND content_id = $id";
    $watchlist_result = mysqli_query($conn, $watchlist_query);
    $in_watchlist = mysqli_num_rows($watchlist_result) > 0;
}

// Get similar TV shows
$similar_query = "SELECT t.*, c.name as category_name FROM tvshows t
                 LEFT JOIN categories c ON t.category_id = c.id
                 WHERE t.category_id = {$tvshow['category_id']} AND t.id != $id
                 ORDER BY RAND()
                 LIMIT 4";
$similar_result = mysqli_query($conn, $similar_query);

// Get selected season (default to 1)
$selected_season = isset($_GET['season']) ? (int)$_GET['season'] : 1;

// Get all seasons
$seasons_query = "SELECT DISTINCT season_number FROM episodes
                 WHERE tvshow_id = $id
                 ORDER BY season_number ASC";
$seasons_result = mysqli_query($conn, $seasons_query);
$seasons = [];
while ($season = mysqli_fetch_assoc($seasons_result)) {
    $seasons[] = $season['season_number'];
}

// If no seasons found, set selected season to 0
if (empty($seasons)) {
    $selected_season = 0;
} else if (!in_array($selected_season, $seasons)) {
    // If selected season is not valid, set to first season
    $selected_season = $seasons[0];
}
?>

<!-- Details Banner -->
<section class="details-banner" style="background-image: url('<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['banner']; ?>')">
    <div class="banner-overlay"></div>
    <div class="container">
        <div class="details-content">
            <div class="row">
                <div class="col-lg-3 col-md-4 mb-4 mb-md-0">
                    <div class="poster-container">
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $tvshow['poster']; ?>" alt="<?php echo $tvshow['title']; ?>" class="details-poster">
                        <?php if($tvshow['premium_only']): ?>
                        <div class="premium-badge">
                            <i class="fas fa-crown"></i> PREMIUM
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-9 col-md-8">
                    <h1 class="details-title"><?php echo $tvshow['title']; ?></h1>
                    <div class="details-meta">
                        <div class="details-rating">
                            <i class="fas fa-star"></i> <?php echo number_format($tvshow['rating'], 1); ?>
                        </div>
                        <span><i class="fas fa-calendar-alt"></i> <?php echo $tvshow['start_year']; ?><?php echo $tvshow['end_year'] ? ' - ' . $tvshow['end_year'] : ' - Present'; ?></span>
                        <span><i class="fas fa-film"></i> <?php echo $tvshow['seasons']; ?> Season<?php echo $tvshow['seasons'] > 1 ? 's' : ''; ?></span>
                        <span><i class="fas fa-tag"></i> <?php echo $tvshow['category_name']; ?></span>
                    </div>

                    <?php if(mysqli_num_rows($genres_result) > 0): ?>
                    <div class="details-genres mt-2">
                        <i class="fas fa-tags me-2"></i>
                        <?php
                        $genres = [];
                        while($genre = mysqli_fetch_assoc($genres_result)) {
                            $genres[] = $genre['name'];
                        }
                        echo implode(', ', $genres);
                        ?>
                    </div>
                    <?php endif; ?>

                    <div class="details-description mt-4">
                        <h4>Overview</h4>
                        <p><?php echo $tvshow['description']; ?></p>
                        <div class="telegram-join mt-3">
                            <a href="https://t.me/buycinepix" target="_blank" class="btn btn-telegram">
                                <i class="fab fa-telegram"></i> টেলিগ্রাম গ্রুপে জয়েন করুন
                            </a>
                        </div>
                    </div>

                    <div class="details-actions mt-4">

                        <?php if(isLoggedIn()): ?>
                            <?php if($in_watchlist): ?>
                            <a href="#" class="btn btn-outline-light remove-from-watchlist" data-id="<?php echo $watchlist_result->fetch_assoc()['id']; ?>">
                                <i class="fas fa-check"></i> In My List
                            </a>
                            <?php else: ?>
                            <a href="#" class="btn btn-outline-light add-to-watchlist" data-id="<?php echo $tvshow['id']; ?>" data-type="tvshow">
                                <i class="fas fa-plus"></i> Add to My List
                            </a>
                            <?php endif; ?>
                        <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-outline-light">
                            <i class="fas fa-plus"></i> Add to My List
                        </a>
                        <?php endif; ?>

                        <a href="#reviews" class="btn btn-outline-light">
                            <i class="fas fa-comment"></i> Reviews
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Content Tabs -->
<section class="py-5">
    <div class="container">
        <div class="section-header mb-4">
            <h2 class="section-title"><i class="fas fa-list me-2"></i>Episodes</h2>
        </div>
        <div class="episodes-content">
                <?php if(!empty($seasons)): ?>
                <!-- Season Selector -->
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2" role="group" aria-label="Season selector">
                        <?php foreach($seasons as $season_num): ?>
                        <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $id; ?>&season=<?php echo $season_num; ?>" class="btn <?php echo ($season_num == $selected_season) ? 'btn-danger' : 'btn-outline-light'; ?>">
                            Season <?php echo $season_num; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Episodes List -->
                <?php
                $episodes_query = "SELECT * FROM episodes
                                  WHERE tvshow_id = $id AND season_number = $selected_season
                                  ORDER BY episode_number ASC";
                $episodes_result = mysqli_query($conn, $episodes_query);

                if(mysqli_num_rows($episodes_result) > 0):
                ?>
                <div class="episode-list">
                    <?php while($episode = mysqli_fetch_assoc($episodes_result)):
                        // Get episode download links (only download links, not streaming links)
                        $download_links_query = "SELECT * FROM episode_links
                                              WHERE episode_id = {$episode['id']}
                                              AND link_type = 'download'
                                              ORDER BY quality DESC, server_name ASC
                                              LIMIT 3";
                        $download_links_result = mysqli_query($conn, $download_links_query);
                        $has_download_links = mysqli_num_rows($download_links_result) > 0;
                    ?>
                    <div class="episode-item">
                        <div class="episode-header">
                            <div class="episode-number">
                                <span>S<?php echo $episode['season_number']; ?>E<?php echo str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT); ?></span>
                            </div>
                            <div class="episode-title">
                                <h5><?php echo $episode['title']; ?></h5>
                                <div class="episode-meta">
                                    <span><i class="fas fa-clock me-1"></i> <?php echo $episode['duration']; ?> min</span>
                                    <?php if($episode['is_premium']): ?>
                                    <span class="premium-tag"><i class="fas fa-crown me-1"></i> PREMIUM</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="episode-content">
                            <?php if(!empty($episode['thumbnail'])): ?>
                            <div class="episode-thumbnail">
                                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $episode['thumbnail']; ?>" alt="<?php echo $episode['title']; ?>">
                            </div>
                            <?php endif; ?>
                            <div class="episode-details">
                                <p class="episode-description"><?php echo substr($episode['description'], 0, 150); ?>...</p>

                                <div class="episode-actions">
                                    <?php
                                    // Get all streaming links for this episode (only manually added links, no auto-generated vidzee links)
                                    $stream_query = "SELECT * FROM episode_links
                                                    WHERE episode_id = {$episode['id']}
                                                    AND link_type = 'stream'
                                                    ORDER BY quality DESC, server_name ASC";
                                    $stream_result = mysqli_query($conn, $stream_query);
                                    $has_stream = mysqli_num_rows($stream_result) > 0;

                                    // Get episode thumbnail or use TV show poster as fallback
                                    $poster_url = SITE_URL . '/uploads/' . (!empty($episode['thumbnail']) ? $episode['thumbnail'] : $tvshow['poster']);
                                    ?>

                                    <?php if ($has_stream): ?>
                                    <!-- Stream button hidden -->
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <?php if($has_download_links): ?>
                        <div class="episode-downloads">
                            <div class="downloads-header">
                                <i class="fas fa-download me-2"></i> Download Links
                            </div>
                            <div class="download-links">
                                <?php while($link = mysqli_fetch_assoc($download_links_result)):
                                    // Get file size from database or use default based on quality
                                    $file_size = '';
                                    if(!empty($link['file_size'])) {
                                        $file_size = $link['file_size'];
                                    } else {
                                        // Fallback to default sizes if not specified
                                        if($link['quality'] == '720p') $file_size = '350 MB';
                                        elseif($link['quality'] == '1080p') $file_size = '700 MB';
                                        elseif($link['quality'] == '4K') $file_size = '1.5 GB';
                                        else $file_size = '200 MB';
                                    }
                                ?>
                                <?php if($link['is_premium'] && !$is_premium): ?>
                                <div class="premium-download-container">
                                    <div class="premium-banner">
                                        <i class="fas fa-crown"></i> PREMIUM
                                    </div>
                                    <a href="<?php echo SITE_URL; ?>/premium.php" class="download-link" target="_blank">
                                        <div class="download-quality"><?php echo $link['quality']; ?></div>
                                        <div class="download-server">
                                            <?php
                                            // Show server name if available, otherwise show link type
                                            if (!empty($link['server_name'])) {
                                                echo $link['server_name'];
                                            } else {
                                                // Determine server name from URL
                                                $url = parse_url($link['link_url']);
                                                $host = isset($url['host']) ? $url['host'] : '';

                                                if (strpos($host, 'drive.google.com') !== false) {
                                                    echo 'Google Drive';
                                                } elseif (strpos($host, 'mega.nz') !== false) {
                                                    echo 'MEGA';
                                                } elseif (strpos($host, 'mediafire.com') !== false) {
                                                    echo 'MediaFire';
                                                } elseif (strpos($host, 'dropbox.com') !== false) {
                                                    echo 'Dropbox';
                                                } else {
                                                    echo 'Direct Link';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="download-size"><?php echo $file_size; ?></div>
                                        <div class="download-button">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                    </a>
                                </div>
                                <?php else: ?>
                                <div class="download-links-container">
                                    <a href="<?php echo $link['link_url']; ?>" class="download-link" target="_blank">
                                        <div class="download-quality"><?php echo $link['quality']; ?></div>
                                        <div class="download-server">
                                            <?php
                                            // Show server name if available, otherwise show link type
                                            if (!empty($link['server_name'])) {
                                                echo $link['server_name'];
                                            } else {
                                                // Determine server name from URL
                                                $url = parse_url($link['link_url']);
                                                $host = isset($url['host']) ? $url['host'] : '';

                                                if (strpos($host, 'drive.google.com') !== false) {
                                                    echo 'Google Drive';
                                                } elseif (strpos($host, 'mega.nz') !== false) {
                                                    echo 'MEGA';
                                                } elseif (strpos($host, 'mediafire.com') !== false) {
                                                    echo 'MediaFire';
                                                } elseif (strpos($host, 'dropbox.com') !== false) {
                                                    echo 'Dropbox';
                                                } else {
                                                    echo 'Direct Link';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="download-size"><?php echo $file_size; ?></div>
                                        <div class="download-button">
                                            <i class="fas fa-download"></i>
                                        </div>
                                    </a>
                                    <?php
                                    // Check if URL is streamable
                                    require_once 'includes/streaming_helper.php';
                                    if (isStreamableUrl($link['link_url'])) {
                                        // Get episode thumbnail or use TV show poster as fallback
                                        $poster_url = SITE_URL . '/uploads/' . (!empty($episode['thumbnail']) ? $episode['thumbnail'] : $tvshow['poster']);

                                        // Generate streaming URL
                                        $play_url = getPlayUrlFromDownload(
                                            $link['link_url'],
                                            $tvshow['title'] . ' - S' . $episode['season_number'] . 'E' . str_pad($episode['episode_number'], 2, '0', STR_PAD_LEFT) . ': ' . $episode['title'],
                                            $poster_url,
                                            $link['is_premium'],
                                            $link['quality'],
                                            $link['server_name']
                                        );
                                    ?>
                                    <a href="<?php echo $play_url; ?>" class="watch-link">
                                        <div class="watch-button">
                                            <i class="fas fa-play"></i> দেখুন
                                        </div>
                                    </a>
                                    <?php } ?>
                                </div>
                                <?php endif; ?>
                                <?php endwhile; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endwhile; ?>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <p class="mb-0">No episodes available for Season <?php echo $selected_season; ?>.</p>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="alert alert-info">
                    <p class="mb-0">No episodes available at the moment.</p>
                </div>
                <?php endif; ?>
        </div>
    </div>
</section>

<!-- Ad Section After Episodes -->
<?php echo renderAdSection('inline', 'Episodes Advertisement', '728x90'); ?>

<!-- Trailer Section -->
<?php if($tvshow['trailer_url']): ?>
<section class="py-5">
    <div class="container">
        <h2 class="section-title">Trailer</h2>
        <div class="trailer-container">
            <iframe src="<?php echo $tvshow['trailer_url']; ?>" allowfullscreen></iframe>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Reviews Section -->
<section class="py-5" id="reviews">
    <div class="container">
        <h2 class="section-title">Reviews</h2>

        <?php if(isLoggedIn()): ?>
        <!-- Review Form -->
        <div class="mb-5">
            <h4>Write a Review</h4>
            <form id="reviewForm">
                <input type="hidden" name="content_type" value="tvshow">
                <input type="hidden" name="content_id" value="<?php echo $id; ?>">
                <div class="mb-3">
                    <label for="rating" class="form-label">Rating</label>
                    <select class="form-select" id="rating" name="rating" required>
                        <option value="">Select rating</option>
                        <?php for($i = 10; $i >= 1; $i--): ?>
                        <option value="<?php echo $i; ?>"><?php echo $i; ?>/10</option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="comment" class="form-label">Your Review</label>
                    <textarea class="form-control" id="comment" name="comment" rows="4" required></textarea>
                </div>
                <button type="submit" class="btn btn-danger">Submit Review</button>
            </form>
        </div>
        <?php else: ?>
        <div class="alert alert-info mb-4">
            <p class="mb-0">Please <a href="<?php echo SITE_URL; ?>/login.php" class="alert-link">login</a> to write a review.</p>
        </div>
        <?php endif; ?>

        <!-- Reviews List -->
        <?php if(mysqli_num_rows($reviews_result) > 0): ?>
            <?php while($review = mysqli_fetch_assoc($reviews_result)): ?>
            <div class="review-card">
                <div class="review-header">
                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $review['profile_image']; ?>" alt="<?php echo $review['username']; ?>" class="review-user-img">
                    <div>
                        <h5 class="review-user-name"><?php echo $review['username']; ?></h5>
                        <p class="review-date"><?php echo date('F j, Y', strtotime($review['created_at'])); ?></p>
                    </div>
                </div>
                <div class="review-rating">
                    <?php for($i = 1; $i <= 10; $i++): ?>
                        <?php if($i <= $review['rating']): ?>
                        <i class="fas fa-star"></i>
                        <?php else: ?>
                        <i class="far fa-star"></i>
                        <?php endif; ?>
                    <?php endfor; ?>
                    <span class="ms-2"><?php echo $review['rating']; ?>/10</span>
                </div>
                <p class="review-text"><?php echo $review['comment']; ?></p>
            </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <p class="mb-0">No reviews yet. Be the first to review!</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Ad Section After Reviews -->
<?php echo renderAdSection('banner', 'TV Show Details Advertisement', '728x90'); ?>

<!-- Similar Shows Carousel Section -->
<section class="similar-shows-section py-5">
    <div class="container">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">আরও দেখতে পারেন</h2>
            <div class="carousel-controls">
                <button class="carousel-control-prev" type="button" id="similar-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-control-next" type="button" id="similar-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <div class="similar-shows-carousel">
            <?php mysqli_data_seek($similar_result, 0); // Reset result pointer ?>
            <?php while($similar = mysqli_fetch_assoc($similar_result)): ?>
            <div class="similar-show-item">
                <a href="<?php echo SITE_URL; ?>/tvshow_details.php?id=<?php echo $similar['id']; ?>" class="similar-show-link">
                    <div class="similar-show-card">
                        <div class="similar-show-poster">
                            <?php if($similar['premium_only']): ?>
                            <div class="premium-tag">
                                <i class="fas fa-crown"></i>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $similar['poster']; ?>" alt="<?php echo $similar['title']; ?>" loading="lazy">
                            <?php if($similar['trailer_url']): ?>
                            <div class="trailer-btn">
                                <a href="#" class="play-trailer" data-trailer="<?php echo $similar['trailer_url']; ?>" onclick="event.preventDefault(); playTrailer('<?php echo $similar['trailer_url']; ?>');">
                                    <i class="fas fa-play"></i>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="similar-show-info">
                            <h3 class="similar-show-title"><?php echo $similar['title']; ?></h3>
                            <div class="similar-show-meta">
                                <span class="year"><?php echo $similar['start_year']; ?><?php echo $similar['end_year'] ? ' - ' . $similar['end_year'] : ''; ?></span>
                                <span class="rating"><i class="fas fa-star"></i> <?php echo number_format($similar['rating'], 1); ?></span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<!-- Trailer Modal -->
<div class="modal fade" id="trailerModal" tabindex="-1" aria-labelledby="trailerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="trailerModalLabel">Watch Trailer</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <iframe id="trailerIframe" src="" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
    // Play trailer functionality
    function playTrailer(trailerUrl) {
        // Create modal element
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'dynamicTrailerModal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content bg-dark">
                    <div class="modal-header border-0">
                        <h5 class="modal-title text-white" id="trailerModalLabel">Show Trailer</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="ratio ratio-16x9">
                            <iframe src="${trailerUrl}" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Initialize and show the modal
        const trailerModal = new bootstrap.Modal(modal);
        trailerModal.show();

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // Initialize similar shows carousel
    document.addEventListener('DOMContentLoaded', function() {
        // Similar shows carousel navigation
        const similarCarousel = document.querySelector('.similar-shows-carousel');
        const prevButton = document.getElementById('similar-prev');
        const nextButton = document.getElementById('similar-next');

        if (similarCarousel && prevButton && nextButton) {
            // Calculate scroll amount based on container width
            const scrollAmount = similarCarousel.offsetWidth * 0.8;

            prevButton.addEventListener('click', () => {
                similarCarousel.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
            });

            nextButton.addEventListener('click', () => {
                similarCarousel.scrollBy({ left: scrollAmount, behavior: 'smooth' });
            });
        }

        // Legacy trailer modal functionality
        const trailerModal = document.getElementById('trailerModal');
        const trailerIframe = document.getElementById('trailerIframe');
        const trailerBtns = document.querySelectorAll('.episode-trailer-btn');

        if (trailerModal && trailerIframe) {
            trailerBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const trailerUrl = this.getAttribute('data-trailer');
                    trailerIframe.src = trailerUrl;
                    const trailerModalObj = new bootstrap.Modal(trailerModal);
                    trailerModalObj.show();
                });
            });

            trailerModal.addEventListener('hidden.bs.modal', function () {
                trailerIframe.src = '';
            });
        }

        // Add to watchlist
        const addToWatchlistBtns = document.querySelectorAll('.add-to-watchlist');
        addToWatchlistBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const contentId = this.getAttribute('data-id');
                const contentType = this.getAttribute('data-type');

                // Send AJAX request to add to watchlist
                fetch('<?php echo SITE_URL; ?>/ajax/watchlist.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=add&content_id=${contentId}&content_type=${contentType}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                    // Update button
                    this.innerHTML = '<i class="fas fa-check"></i> In My List';
                    this.classList.remove('add-to-watchlist');
                    this.classList.add('remove-from-watchlist');
                    this.setAttribute('data-id', data.watchlist_id);

                    // Show success message
                    alert('Added to your watchlist!');
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        });
    });

        // Remove from watchlist
        const removeFromWatchlistBtns = document.querySelectorAll('.remove-from-watchlist');
        removeFromWatchlistBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const watchlistId = this.getAttribute('data-id');

                // Send AJAX request to remove from watchlist
                fetch('<?php echo SITE_URL; ?>/ajax/watchlist.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=remove&watchlist_id=${watchlistId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update button
                        this.innerHTML = '<i class="fas fa-plus"></i> Add to My List';
                        this.classList.remove('remove-from-watchlist');
                        this.classList.add('add-to-watchlist');
                        this.setAttribute('data-id', data.content_id);
                        this.setAttribute('data-type', data.content_type);

                        // Show success message
                        alert('Removed from your watchlist!');
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
        });
    });

        // Submit review
        const reviewForm = document.getElementById('reviewForm');
        if (reviewForm) {
            reviewForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const formDataObj = {};
                formData.forEach((value, key) => {
                    formDataObj[key] = value;
                });

                // Send AJAX request to submit review
                fetch('<?php echo SITE_URL; ?>/ajax/reviews.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(formDataObj).toString()
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message and reload page to show new review
                        alert('Review submitted successfully!');
                        location.reload();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        }
    });

    // Play trailer functionality for similar shows
    function playTrailer(trailerUrl) {
        // Create modal element
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'dynamicTrailerModal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content bg-dark">
                    <div class="modal-header border-0">
                        <h5 class="modal-title text-white" id="trailerModalLabel">Show Trailer</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="ratio ratio-16x9">
                            <iframe src="${trailerUrl}" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Initialize and show the modal
        const trailerModal = new bootstrap.Modal(modal);
        trailerModal.show();

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // Make similar show cards fully clickable
    document.addEventListener('DOMContentLoaded', function() {
        const similarShowCards = document.querySelectorAll('.movie-card');
        similarShowCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on a button or link inside the card
                if (!e.target.closest('.movie-card-btn') && !e.target.closest('a:not(.movie-card-link)')) {
                    const cardLink = this.closest('.movie-card-link');
                    if (cardLink) {
                        window.location.href = cardLink.href;
                    }
                }
            });
        });

        // Handle trailer buttons in similar shows section
        const trailerButtons = document.querySelectorAll('.play-trailer');
        trailerButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const trailerUrl = this.getAttribute('data-trailer');
                playTrailer(trailerUrl);
            });
        });
    });
</script>
