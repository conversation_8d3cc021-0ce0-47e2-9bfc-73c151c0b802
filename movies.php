<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Get category filter
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 12;
$offset = ($page - 1) * $items_per_page;

// Build query
$query = "SELECT m.*, c.name as category_name FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE m.is_active = 1";

// Add category filter if set
if ($category_filter > 0) {
    $query .= " AND m.category_id = $category_filter";
}

// Add sorting
$query .= " ORDER BY m.release_year DESC";

// Get total count for pagination
$count_query = $query;
$count_result = mysqli_query($conn, $count_query);
$total_items = mysqli_num_rows($count_result);
$total_pages = ceil($total_items / $items_per_page);

// Add limit for pagination
$query .= " LIMIT $offset, $items_per_page";

// Execute query
$result = mysqli_query($conn, $query);

// Get all categories for filter
$categories_query = "SELECT * FROM categories ORDER BY name";
$categories_result = mysqli_query($conn, $categories_query);

// Get current category name if filter is active
$current_category_name = "All Movies";
if ($category_filter > 0) {
    $cat_query = "SELECT name FROM categories WHERE id = $category_filter";
    $cat_result = mysqli_query($conn, $cat_query);
    if (mysqli_num_rows($cat_result) > 0) {
        $cat = mysqli_fetch_assoc($cat_result);
        $current_category_name = $cat['name'] . " Movies";
    }
}

// Add custom CSS for movies page
echo '<link rel="stylesheet" href="' . SITE_URL . '/css/movies.css">';
?>

<!-- Page Header -->
<section class="movies-header">
    <div class="container">
        <div class="movies-header-content">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="movies-title"><?php echo $current_category_name; ?></h1>
                    <p class="movies-subtitle">Discover our collection of the latest and greatest movies across various genres. Find your next favorite film to watch.</p>
                </div>
                <div class="col-lg-6">
                    <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="search-form ms-auto">
                        <input type="hidden" name="type" value="movie">
                        <input type="search" name="query" class="form-control" placeholder="Search for movies...">
                        <button type="submit" class="btn"><i class="fas fa-search"></i></button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Top Ad Section -->
<?php echo renderAdSection('banner', 'Movies Page Top Advertisement', '728x90'); ?>

<!-- Category Filter -->
<section class="py-4">
    <div class="container">
        <div class="category-filter">
            <h3 class="filter-title"><i class="fas fa-filter"></i> Filter by Category</h3>
            <div class="filter-buttons">
                <a href="movies.php" class="filter-btn <?php echo $category_filter == 0 ? 'active' : ''; ?>">All</a>
                <?php
                // Reset pointer to beginning of result set
                mysqli_data_seek($categories_result, 0);

                // Debug: Print category filter value
                // echo "<div style='color:white'>Current filter: $category_filter</div>";

                // Fetch all categories again
                while($category = mysqli_fetch_assoc($categories_result)):
                    // Debug: Print category ID
                    // echo "<div style='color:white'>Category ID: {$category['id']}</div>";
                ?>
                <a href="movies.php?category=<?php echo $category['id']; ?>" class="filter-btn <?php echo ($category_filter == $category['id']) ? 'active' : ''; ?>"><?php echo $category['name']; ?></a>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</section>

<!-- Movies Grid -->
<section class="movies-grid py-5">
    <div class="container">
        <?php if(mysqli_num_rows($result) > 0): ?>
            <div class="row">
                <?php while($movie = mysqli_fetch_assoc($result)): ?>
                <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-3">
                    <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>" class="movie-card-link">
                        <div class="movie-card" data-category="<?php echo $movie['category_id']; ?>">
                            <?php if($movie['premium_only']): ?>
                            <div class="premium-badge">
                                <i class="fas fa-crown"></i>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo $movie['title']; ?>">
                            <div class="movie-card-overlay">
                                <h5 class="movie-card-title"><?php echo $movie['title']; ?></h5>
                                <div class="movie-card-info">
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo $movie['release_year']; ?></span> •
                                    <span><i class="fas fa-film"></i> <?php echo $movie['category_name']; ?></span>
                                </div>
                                <div class="movie-card-rating">
                                    <i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?>
                                </div>
                            </div>
                            <div class="movie-card-buttons">
                                <span class="movie-card-btn" title="More Info">
                                    <i class="fas fa-info-circle"></i>
                                </span>
                                <span class="movie-card-btn add-to-watchlist" data-id="<?php echo $movie['id']; ?>" data-type="movie" title="Add to Watchlist" onclick="event.stopPropagation(); addToWatchlist(<?php echo $movie['id']; ?>, 'movie'); return false;">
                                    <i class="fas fa-plus"></i>
                                </span>
                                <?php if($movie['trailer_url']): ?>
                                <span class="movie-card-btn play-trailer" data-trailer="<?php echo $movie['trailer_url']; ?>" title="Play Trailer" onclick="event.stopPropagation(); playTrailer('<?php echo $movie['trailer_url']; ?>'); return false;">
                                    <i class="fas fa-play"></i>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
                <?php endwhile; ?>
            </div>

            <!-- Middle Ad Section -->
            <?php echo renderAdSection('inline', 'Movies Grid Advertisement', '728x90'); ?>

            <!-- Pagination -->
            <?php if($total_pages > 1): ?>
            <div class="pagination-container">
                <ul class="pagination">
                    <?php if($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="movies.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $page - 1; ?>" aria-label="Previous">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    // Show limited page numbers with ellipsis
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1) {
                        echo '<li class="page-item"><a class="page-link" href="movies.php?' . ($category_filter > 0 ? 'category=' . $category_filter . '&' : '') . 'page=1">1</a></li>';
                        if ($start_page > 2) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                    }

                    for($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="movies.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor;

                    if ($end_page < $total_pages) {
                        if ($end_page < $total_pages - 1) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                        echo '<li class="page-item"><a class="page-link" href="movies.php?' . ($category_filter > 0 ? 'category=' . $category_filter . '&' : '') . 'page=' . $total_pages . '">' . $total_pages . '</a></li>';
                    }
                    ?>

                    <?php if($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="movies.php?<?php echo $category_filter > 0 ? 'category=' . $category_filter . '&' : ''; ?>page=<?php echo $page + 1; ?>" aria-label="Next">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="no-movies-message">
                <i class="fas fa-film"></i>
                <h4>No Movies Found</h4>
                <p>There are no movies available in this category. Please try another category or check back later.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>

<script src="<?php echo SITE_URL; ?>/js/movies.js"></script>
