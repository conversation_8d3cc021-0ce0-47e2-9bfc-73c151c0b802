<?php
/**
 * Ad Placeholder Component with Premium User Check
 * Usage: include 'includes/ad_placeholder.php'; echo renderAdPlaceholder('banner', 'Top Banner Ad');
 */

/**
 * Check if current user is premium
 */
function isPremiumUser() {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    global $conn;
    $user_id = $_SESSION['user_id'];

    $query = "SELECT premium_expires FROM users WHERE id = ? AND premium_expires > NOW()";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    return mysqli_num_rows($result) > 0;
}

/**
 * Render Real Ads (Premium users won't see ads)
 */
function renderRealAd($adType = 'banner') {
    // Don't show ads to premium users
    if (isPremiumUser()) {
        return '';
    }

    $adCode = '';

    switch($adType) {
        case 'banner_728x90':
            $adCode = '
            <div class="ad-container ad-banner">
                <script type="text/javascript">
                    atOptions = {
                        \'key\' : \'735a559a5872816da47237a603cac4ad\',
                        \'format\' : \'iframe\',
                        \'height\' : 90,
                        \'width\' : 728,
                        \'params\' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></script>
            </div>';
            break;

        case 'sidebar_300x250':
            $adCode = '
            <div class="ad-container ad-sidebar">
                <script type="text/javascript">
                    atOptions = {
                        \'key\' : \'7d3b7accac0194a88ccf420c241ec7aa\',
                        \'format\' : \'iframe\',
                        \'height\' : 250,
                        \'width\' : 300,
                        \'params\' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/7d3b7accac0194a88ccf420c241ec7aa/invoke.js"></script>
            </div>';
            break;

        case 'medium_468x60':
            $adCode = '
            <div class="ad-container ad-inline">
                <script type="text/javascript">
                    atOptions = {
                        \'key\' : \'ceac305b755cbace9181e4d593e3700b\',
                        \'format\' : \'iframe\',
                        \'height\' : 60,
                        \'width\' : 468,
                        \'params\' : {}
                    };
                </script>
                <script type="text/javascript" src="//www.highperformanceformat.com/ceac305b755cbace9181e4d593e3700b/invoke.js"></script>
            </div>';
            break;

        case 'native_1':
            $adCode = '
            <div class="ad-container ad-native">
                <script type=\'text/javascript\' src=\'//pl27076825.profitableratecpm.com/db/ba/2e/dbba2edda331c47423c9b3fc68f95fb1.js\'></script>
            </div>';
            break;

        case 'native_2':
            $adCode = '
            <div class="ad-container ad-native">
                <script type=\'text/javascript\' src=\'//pl27076956.profitableratecpm.com/fd/6c/f8/fd6cf8e64921887d3713ef08ffd94b55.js\'></script>
            </div>';
            break;
    }

    return $adCode;
}

function renderAdPlaceholder($type = 'banner', $label = 'Advertisement Space', $size = '') {
    $adTypes = [
        'banner' => ['class' => 'ad-banner', 'icon' => 'fas fa-rectangle-ad', 'default_size' => '728x90'],
        'sidebar' => ['class' => 'ad-sidebar', 'icon' => 'fas fa-square-poll-vertical', 'default_size' => '300x250'],
        'inline' => ['class' => 'ad-inline', 'icon' => 'fas fa-bullhorn', 'default_size' => '728x90'],
        'square' => ['class' => 'ad-square', 'icon' => 'fas fa-square', 'default_size' => '250x250'],
        'leaderboard' => ['class' => 'ad-leaderboard', 'icon' => 'fas fa-rectangle-ad', 'default_size' => '728x90'],
        'skyscraper' => ['class' => 'ad-skyscraper', 'icon' => 'fas fa-rectangle-ad', 'default_size' => '160x600']
    ];
    
    $adConfig = $adTypes[$type] ?? $adTypes['banner'];
    $displaySize = $size ?: $adConfig['default_size'];
    
    ob_start();
    ?>
    <div class="ad-container <?php echo $adConfig['class']; ?>">
        <div class="ad-placeholder">
            <i class="<?php echo $adConfig['icon']; ?>"></i>
            <span><?php echo htmlspecialchars($label); ?> (<?php echo $displaySize; ?>)</span>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function renderAdSection($type = 'banner', $label = 'Advertisement Space', $size = '', $containerClass = 'container my-4') {
    ob_start();
    ?>
    <div class="<?php echo $containerClass; ?>">
        <?php echo renderAdPlaceholder($type, $label, $size); ?>
    </div>
    <?php
    return ob_get_clean();
}

function renderDoubleAdSection($leftLabel = 'Advertisement Left', $rightLabel = 'Advertisement Right', $containerClass = 'container my-4') {
    ob_start();
    ?>
    <div class="<?php echo $containerClass; ?>">
        <div class="row">
            <div class="col-md-6 mb-3">
                <?php echo renderAdPlaceholder('square', $leftLabel, '350x250'); ?>
            </div>
            <div class="col-md-6 mb-3">
                <?php echo renderAdPlaceholder('square', $rightLabel, '350x250'); ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function renderSidebarAd($label = 'Sidebar Advertisement', $size = '300x250') {
    return renderAdPlaceholder('sidebar', $label, $size);
}

function renderBannerAd($label = 'Banner Advertisement', $size = '728x90') {
    return renderAdPlaceholder('banner', $label, $size);
}

function renderInlineAd($label = 'Inline Advertisement', $size = '728x90') {
    return renderAdPlaceholder('inline', $label, $size);
}

// Quick usage functions for common ad placements
function topBannerAd() {
    return renderAdSection('banner', 'Top Banner Advertisement', '728x90');
}

function bottomBannerAd() {
    return renderAdSection('banner', 'Bottom Banner Advertisement', '728x90');
}

function sidebarAd() {
    return renderAdPlaceholder('sidebar', 'Sidebar Advertisement', '300x250');
}

function inlineContentAd() {
    return renderAdSection('inline', 'Content Advertisement', '728x90');
}

function footerDoubleAd() {
    return renderDoubleAdSection('Footer Left Ad', 'Footer Right Ad');
}

// Responsive ad function
function responsiveAd($mobileLabel = 'Mobile Ad', $desktopLabel = 'Desktop Ad') {
    ob_start();
    ?>
    <div class="ad-container ad-responsive">
        <div class="d-block d-md-none">
            <?php echo renderAdPlaceholder('banner', $mobileLabel, '320x50'); ?>
        </div>
        <div class="d-none d-md-block">
            <?php echo renderAdPlaceholder('banner', $desktopLabel, '728x90'); ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
?>
