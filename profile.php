<?php
require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Save current page as redirect destination after login
    $_SESSION['redirect_to'] = SITE_URL . '/profile.php';
    redirect(SITE_URL . '/login.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$user_query = "SELECT * FROM users WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);
$user = mysqli_fetch_assoc($user_result);

// Get user stats
$watchlist_count_query = "SELECT COUNT(*) as count FROM watchlist WHERE user_id = $user_id";
$watchlist_count_result = mysqli_query($conn, $watchlist_count_query);
$watchlist_count = mysqli_fetch_assoc($watchlist_count_result)['count'];

$reviews_count_query = "SELECT COUNT(*) as count FROM reviews WHERE user_id = $user_id";
$reviews_count_result = mysqli_query($conn, $reviews_count_query);
$reviews_count = mysqli_fetch_assoc($reviews_count_result)['count'];

// Get active subscription details if premium
$active_subscription = null;
if (isPremium()) {
    $subscription_query = "SELECT s.*, p.name as plan_name, p.price, p.features
                          FROM subscriptions s
                          JOIN premium_plans p ON s.plan_id = p.id
                          WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
                          ORDER BY s.end_date DESC LIMIT 1";
    $subscription_result = mysqli_query($conn, $subscription_query);

    if (mysqli_num_rows($subscription_result) > 0) {
        $active_subscription = mysqli_fetch_assoc($subscription_result);
    }
}

// Get recent payment history
$payment_query = "SELECT p.*, s.plan_id, pp.name as plan_name
                FROM payments p
                JOIN subscriptions s ON p.subscription_id = s.id
                JOIN premium_plans pp ON s.plan_id = pp.id
                WHERE p.user_id = $user_id
                ORDER BY p.payment_date DESC LIMIT 5";
$payment_result = mysqli_query($conn, $payment_query);

// Process profile update
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Handle profile image upload
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
            $error_message = 'Only JPG, PNG and GIF images are allowed.';
        } elseif ($_FILES['profile_image']['size'] > $max_size) {
            $error_message = 'Image size should be less than 2MB.';
        } else {
            $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
            $new_filename = 'user_' . $user_id . '_' . time() . '.' . $file_extension;
            $upload_path = 'uploads/' . $new_filename;

            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                // Update user profile image in database
                $update_image_query = "UPDATE users SET profile_image = '$new_filename' WHERE id = $user_id";
                if (mysqli_query($conn, $update_image_query)) {
                    // Update session variable
                    $_SESSION['profile_image'] = $new_filename;
                    $success_message = 'Profile image updated successfully.';
                } else {
                    $error_message = 'Error updating profile image: ' . mysqli_error($conn);
                }
            } else {
                $error_message = 'Error uploading image.';
            }
        }
    }

    // Handle password update
    if (!empty($_POST['current_password']) && !empty($_POST['new_password']) && !empty($_POST['confirm_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Verify current password
        if (!password_verify($current_password, $user['password'])) {
            $error_message = 'Current password is incorrect.';
        } elseif ($new_password != $confirm_password) {
            $error_message = 'New passwords do not match.';
        } elseif (strlen($new_password) < 6) {
            $error_message = 'New password must be at least 6 characters long.';
        } else {
            // Hash new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

            // Update password in database
            $update_password_query = "UPDATE users SET password = '$hashed_password' WHERE id = $user_id";
            if (mysqli_query($conn, $update_password_query)) {
                $success_message = 'Password updated successfully.';
            } else {
                $error_message = 'Error updating password: ' . mysqli_error($conn);
            }
        }
    }
}

// Get user's recent activity
$activity_query = "SELECT 'watchlist' as type, w.added_at as date,
                  CASE
                      WHEN w.content_type = 'movie' THEN m.title
                      ELSE t.title
                  END as title,
                  w.content_type, w.content_id
                  FROM watchlist w
                  LEFT JOIN movies m ON w.content_type = 'movie' AND w.content_id = m.id
                  LEFT JOIN tvshows t ON w.content_type = 'tvshow' AND w.content_id = t.id
                  WHERE w.user_id = $user_id
                  UNION
                  SELECT 'review' as type, r.created_at as date,
                  CASE
                      WHEN r.content_type = 'movie' THEN m.title
                      ELSE t.title
                  END as title,
                  r.content_type, r.content_id
                  FROM reviews r
                  LEFT JOIN movies m ON r.content_type = 'movie' AND r.content_id = m.id
                  LEFT JOIN tvshows t ON r.content_type = 'tvshow' AND r.content_id = t.id
                  WHERE r.user_id = $user_id
                  ORDER BY date DESC
                  LIMIT 5";
$activity_result = mysqli_query($conn, $activity_query);
?>

<!-- Profile Header -->
<section class="profile-hero py-5">
    <div class="container">
        <div class="profile-header-card">
            <div class="row g-0 align-items-center">
                <div class="col-lg-4 col-md-5 text-center mb-4 mb-md-0">
                    <div class="profile-image-container">
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $user['profile_image']; ?>" alt="<?php echo $user['username']; ?>" class="profile-img">
                        <div class="profile-image-overlay">
                            <label for="profileImage" class="edit-profile-btn">
                                <i class="fas fa-camera"></i>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8 col-md-7">
                    <div class="profile-info px-3 px-md-4">
                        <h1 class="profile-name"><?php echo $user['username']; ?></h1>
                        <p class="profile-email"><i class="fas fa-envelope me-2"></i><?php echo $user['email']; ?></p>
                        <p class="member-since"><i class="fas fa-calendar-alt me-2"></i>Member since <?php echo date('F Y', strtotime($user['created_at'])); ?></p>

                        <div class="profile-stats">
                            <div class="profile-stat">
                                <div class="profile-stat-value"><?php echo $watchlist_count; ?></div>
                                <div class="profile-stat-label">Watchlist</div>
                            </div>
                            <div class="profile-stat">
                                <div class="profile-stat-value"><?php echo $reviews_count; ?></div>
                                <div class="profile-stat-label">Reviews</div>
                            </div>
                            <div class="profile-stat premium-stat">
                                <div class="profile-stat-value">
                                    <?php
                                    $is_premium_status = isPremium();

                                    if($is_premium_status): ?>
                                        <i class="fas fa-crown text-warning"></i> Premium
                                    <?php else: ?>
                                        <i class="fas fa-times"></i> Free
                                    <?php endif; ?>
                                </div>
                                <div class="profile-stat-label">Status</div>
                            </div>
                        </div>

                        <?php if($active_subscription): ?>
                        <div class="active-package mt-3">
                            <div class="package-info">
                                <h6 class="package-title"><i class="fas fa-crown text-warning me-2"></i><?php echo $active_subscription['plan_name']; ?> Package</h6>
                                <div class="package-details">
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="package-info-details">
                                            <div class="info-item">
                                                <span class="info-label">Price:</span>
                                                <span class="package-price">৳<?php echo $active_subscription['price']; ?>/month</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Started:</span>
                                                <span class="package-date"><?php echo date('d M Y', strtotime($active_subscription['start_date'])); ?></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">Expires:</span>
                                                <span class="package-expiry"><?php echo date('d M Y', strtotime($active_subscription['end_date'])); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="package-features">
                                            <h6 class="features-title">Features:</h6>
                                            <ul class="features-list">
                                                <?php foreach(explode("\n", $active_subscription['features']) as $feature): ?>
                                                <li><?php echo $feature; ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="profile-actions mt-3">
                            <a href="<?php echo SITE_URL; ?>/watchlist.php" class="btn btn-sm btn-outline-light me-2">
                                <i class="fas fa-list me-1"></i> My Watchlist
                            </a>
                            <a href="<?php echo SITE_URL; ?>/payment_history.php" class="btn btn-sm btn-outline-light me-2">
                                <i class="fas fa-receipt me-1"></i> Payment History
                            </a>
                            <?php if(!isPremium()): ?>
                            <a href="<?php echo SITE_URL; ?>/payment.php" class="btn btn-sm btn-warning">
                                <i class="fas fa-crown me-1"></i> Upgrade to Premium
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Profile Content -->
<section class="profile-content py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Left Column - Recent Activity -->
            <div class="col-lg-4 col-md-6 order-2 order-md-1">
                <div class="profile-card activity-card">
                    <div class="profile-card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="profile-card-body">
                        <?php if(mysqli_num_rows($activity_result) > 0): ?>
                            <ul class="activity-list">
                                <?php while($activity = mysqli_fetch_assoc($activity_result)): ?>
                                <li class="activity-item">
                                    <div class="activity-content">
                                        <div class="activity-icon">
                                            <?php if($activity['type'] == 'watchlist'): ?>
                                                <i class="fas fa-plus-circle text-success"></i>
                                            <?php else: ?>
                                                <i class="fas fa-star text-warning"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="activity-details">
                                            <div class="activity-action">
                                                <?php if($activity['type'] == 'watchlist'): ?>
                                                    Added to watchlist
                                                <?php else: ?>
                                                    Wrote a review
                                                <?php endif; ?>
                                                <span class="activity-date"><?php echo date('M j', strtotime($activity['date'])); ?></span>
                                            </div>
                                            <a href="<?php echo SITE_URL; ?>/<?php echo $activity['content_type'] == 'movie' ? 'movie_details.php?id=' : 'tvshow_details.php?id='; ?><?php echo $activity['content_id']; ?>" class="activity-title">
                                                <?php echo $activity['title']; ?>
                                            </a>
                                        </div>
                                    </div>
                                </li>
                                <?php endwhile; ?>
                            </ul>
                            <div class="text-center mt-3">
                                <a href="<?php echo SITE_URL; ?>/watchlist.php" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-list me-1"></i> View All
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-film empty-icon"></i>
                                <p>No recent activity found</p>
                                <a href="<?php echo SITE_URL; ?>" class="btn btn-sm btn-outline-danger">Browse Content</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column - Profile Settings -->
            <div class="col-lg-8 col-md-6 order-1 order-md-2">
                <?php if($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Profile Settings Tabs -->
                <div class="profile-tabs">
                    <ul class="nav nav-tabs profile-nav-tabs" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-pane" type="button" role="tab" aria-controls="profile-pane" aria-selected="true">
                                <i class="fas fa-user me-2"></i>Profile
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security-pane" type="button" role="tab" aria-controls="security-pane" aria-selected="false">
                                <i class="fas fa-lock me-2"></i>Security
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments-pane" type="button" role="tab" aria-controls="payments-pane" aria-selected="false">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content profile-tab-content" id="profileTabsContent">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                            <div class="profile-card">
                                <div class="profile-card-header">
                                    <h5 class="mb-0"><i class="fas fa-image me-2"></i>Profile Picture</h5>
                                </div>
                                <div class="profile-card-body">
                                    <form method="POST" action="" enctype="multipart/form-data" class="profile-form">
                                        <div class="row align-items-center">
                                            <div class="col-md-4 text-center mb-3 mb-md-0">
                                                <div class="profile-preview-container">
                                                    <img id="imagePreview" src="<?php echo SITE_URL; ?>/uploads/<?php echo $user['profile_image']; ?>" alt="Profile Preview" class="profile-preview-img">
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="mb-3">
                                                    <label for="profileImage" class="form-label">Upload new profile picture</label>
                                                    <input class="form-control custom-file-input" type="file" id="profileImage" name="profile_image" accept="image/*">
                                                    <div class="form-text"><i class="fas fa-info-circle me-1"></i>Maximum file size: 2MB. Supported formats: JPG, PNG, GIF.</div>
                                                </div>
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="fas fa-upload me-1"></i> Update Profile Picture
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane fade" id="security-pane" role="tabpanel" aria-labelledby="security-tab" tabindex="0">
                            <div class="profile-card">
                                <div class="profile-card-header">
                                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>Change Password</h5>
                                </div>
                                <div class="profile-card-body">
                                    <form method="POST" action="" class="profile-form">
                                        <div class="mb-3">
                                            <label for="currentPassword" class="form-label">Current Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="newPassword" class="form-label">New Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                                <input type="password" class="form-control" id="newPassword" name="new_password" required>
                                            </div>
                                            <div class="form-text"><i class="fas fa-info-circle me-1"></i>Password must be at least 6 characters long.</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-check-double"></i></span>
                                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-save me-1"></i> Change Password
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Payments Tab -->
                        <div class="tab-pane fade" id="payments-pane" role="tabpanel" aria-labelledby="payments-tab" tabindex="0">
                            <?php if($active_subscription): ?>
                            <div class="profile-card mb-4">
                                <div class="profile-card-header">
                                    <h5 class="mb-0"><i class="fas fa-crown text-warning me-2"></i>Active Subscription</h5>
                                </div>
                                <div class="profile-card-body">
                                    <div class="active-subscription-details">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="subscription-info">
                                                    <h6 class="subscription-plan"><?php echo $active_subscription['plan_name']; ?> Plan</h6>
                                                    <p class="subscription-price"><strong>Price:</strong> ৳<?php echo $active_subscription['price']; ?>/month</p>
                                                    <p class="subscription-dates">
                                                        <strong>Started:</strong> <?php echo date('d M Y', strtotime($active_subscription['start_date'])); ?><br>
                                                        <strong>Expires:</strong> <?php echo date('d M Y', strtotime($active_subscription['end_date'])); ?>
                                                    </p>
                                                    <div class="subscription-status">
                                                        <span class="badge bg-success">Active</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="subscription-features">
                                                    <h6>Features:</h6>
                                                    <ul class="features-list">
                                                        <?php foreach(explode("\n", $active_subscription['features']) as $feature): ?>
                                                        <li><i class="fas fa-check text-success me-2"></i><?php echo $feature; ?></li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="profile-card">
                                <div class="profile-card-header">
                                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Recent Payments</h5>
                                </div>
                                <div class="profile-card-body">
                                    <?php if(mysqli_num_rows($payment_result) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-dark table-hover payment-history-table">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Plan</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while($payment = mysqli_fetch_assoc($payment_result)): ?>
                                                <tr>
                                                    <td><?php echo date('d M Y', strtotime($payment['payment_date'])); ?></td>
                                                    <td><?php echo $payment['plan_name']; ?></td>
                                                    <td>৳<?php echo $payment['amount']; ?></td>
                                                    <td>
                                                        <?php
                                                        $method_icon = '';
                                                        switch($payment['payment_method']) {
                                                            case 'bkash':
                                                                $method_icon = 'text-danger';
                                                                break;
                                                            case 'nagad':
                                                                $method_icon = 'text-warning';
                                                                break;
                                                            case 'rocket':
                                                                $method_icon = 'text-primary';
                                                                break;
                                                            default:
                                                                $method_icon = 'text-secondary';
                                                        }
                                                        ?>
                                                        <span class="<?php echo $method_icon; ?>">
                                                            <?php echo ucfirst($payment['payment_method']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $status_class = '';
                                                        switch($payment['payment_status']) {
                                                            case 'completed':
                                                                $status_class = 'bg-success';
                                                                break;
                                                            case 'pending':
                                                                $status_class = 'bg-warning text-dark';
                                                                break;
                                                            case 'failed':
                                                                $status_class = 'bg-danger';
                                                                break;
                                                            case 'refunded':
                                                                $status_class = 'bg-info';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $status_class; ?>">
                                                            <?php echo ucfirst($payment['payment_status']); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="<?php echo SITE_URL; ?>/payment_history.php" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-list me-1"></i> View All Payments
                                        </a>
                                    </div>
                                    <?php else: ?>
                                    <div class="empty-state">
                                        <i class="fas fa-receipt empty-icon"></i>
                                        <p>No payment history found</p>
                                        <?php if(!isPremium()): ?>
                                        <a href="<?php echo SITE_URL; ?>/payment.php" class="btn btn-sm btn-warning">
                                            <i class="fas fa-crown me-1"></i> Upgrade to Premium
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
