<?php
require_once 'includes/header.php';
require_once 'includes/ad_placeholder.php';

// Get premium plans
$plans_query = "SELECT * FROM premium_plans ORDER BY price";
$plans_result = mysqli_query($conn, $plans_query);

// Check if user is already premium using isPremium() function
$is_premium = false;
$current_subscription = null;
if (isLoggedIn()) {
    // This will update the user's premium status based on subscriptions
    $is_premium = isPremium();

    // Get current subscription details if premium
    if ($is_premium) {
        $user_id = $_SESSION['user_id'];
        $subscription_query = "SELECT s.*, p.name as plan_name, p.price, p.features
                              FROM subscriptions s
                              JOIN premium_plans p ON s.plan_id = p.id
                              WHERE s.user_id = $user_id AND s.status = 'active' AND s.end_date > NOW()
                              ORDER BY s.end_date DESC LIMIT 1";
        $subscription_result = mysqli_query($conn, $subscription_query);

        if (mysqli_num_rows($subscription_result) > 0) {
            $current_subscription = mysqli_fetch_assoc($subscription_result);
        }
    }
}
?>

<!-- Hero Section -->
<section class="py-5 bg-dark text-center">
    <div class="container">
        <h1 class="display-4 fw-bold">Upgrade to Premium</h1>
        <p class="lead text-muted">Enjoy unlimited access to premium content with no restrictions</p>
    </div>
</section>

<!-- Top Ad Section -->
<?php echo renderAdSection('banner', 'Premium Page Advertisement', '728x90'); ?>

<!-- Current Subscription (if premium) -->
<?php if ($is_premium): ?>
<section class="py-5 bg-dark">
    <div class="container">
        <div class="alert alert-success">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4>You are currently on the <?php echo $current_subscription['plan_name']; ?> plan</h4>
                    <p class="mb-0">Your subscription is valid until <?php echo date('F j, Y', strtotime($current_subscription['end_date'])); ?></p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="<?php echo SITE_URL; ?>/premium_content.php" class="btn btn-success">View Premium Content</a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>



<!-- Custom CSS for Premium Plans -->
<style>
    /* Premium Plan Cards */
    .pricing-card {
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        border: 2px solid rgba(255, 255, 255, 0.1);
        background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(220, 53, 69, 0.3);
        border-color: rgba(220, 53, 69, 0.5);
    }

    .pricing-card.popular {
        border-color: #dc3545;
        transform: scale(1.05);
        z-index: 1;
    }

    .pricing-card.popular:hover {
        transform: scale(1.05) translateY(-10px);
    }

    .pricing-header {
        background: linear-gradient(45deg, #dc3545, #e83e8c);
        padding: 25px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .pricing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 80%);
        opacity: 0.3;
    }

    .pricing-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .pricing-price {
        font-size: 3rem;
        font-weight: 800;
        margin: 15px 0 5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .pricing-duration {
        font-size: 1.1rem;
        opacity: 0.8;
    }

    .pricing-body {
        padding: 25px;
    }

    .pricing-features {
        margin-bottom: 25px;
    }

    .pricing-feature-item {
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
    }

    .pricing-feature-item:last-child {
        border-bottom: none;
    }

    .feature-icon {
        width: 24px;
        height: 24px;
        background-color: rgba(40, 167, 69, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .feature-icon i {
        font-size: 12px;
    }

    .pricing-footer {
        padding: 0 25px 25px;
    }

    .btn-subscribe {
        border-radius: 50px;
        padding: 12px 30px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-subscribe::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    .btn-subscribe:focus:not(:active)::after {
        animation: ripple 1s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }

    .popular-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: #ffc107;
        color: #000;
        padding: 5px 15px;
        font-size: 0.8rem;
        font-weight: 600;
        transform: rotate(45deg) translateX(3.5rem) translateY(-1rem);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        z-index: 2;
    }

    /* Comparison Table */
    .comparison-toggle {
        margin-bottom: 30px;
    }

    .comparison-table {
        display: none;
        margin-top: 40px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .comparison-table th {
        background: linear-gradient(45deg, #343a40, #495057);
        color: white;
        text-align: center;
        padding: 15px;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .comparison-table td {
        text-align: center;
        padding: 12px;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .comparison-table tr:nth-child(odd) {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .comparison-table tr:hover {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .feature-available {
        color: #28a745;
    }

    .feature-unavailable {
        color: #dc3545;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .pricing-card.popular {
            transform: scale(1);
        }

        .pricing-card.popular:hover {
            transform: translateY(-10px);
        }
    }

    @media (max-width: 768px) {
        .pricing-price {
            font-size: 2.5rem;
        }

        .pricing-title {
            font-size: 1.5rem;
        }
    }
</style>

<!-- Pricing Plans -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold mb-3">আপনার প্ল্যান বেছে নিন</h2>
            <p class="lead text-muted">আমাদের প্রিমিয়াম প্ল্যানগুলি আপনাকে সেরা অভিজ্ঞতা প্রদান করবে</p>
        </div>

        <!-- Plan Comparison Toggle -->
        <div class="text-center comparison-toggle mb-4">
            <button class="btn btn-outline-light" id="toggleComparison">
                <i class="fas fa-table me-2"></i> প্ল্যান তুলনা দেখুন
            </button>
        </div>

        <!-- Pricing Cards -->
        <div class="row justify-content-center">
            <?php
            // Reset the result pointer
            mysqli_data_seek($plans_result, 0);

            // Count plans to identify the middle one as popular
            $total_plans = mysqli_num_rows($plans_result);
            $popular_index = ceil($total_plans / 2) - 1;
            $current_index = 0;

            while($plan = mysqli_fetch_assoc($plans_result)):
                $is_popular = ($current_index == $popular_index);
            ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card h-100 <?php echo $is_popular ? 'popular' : ''; ?>">
                    <?php if($is_popular): ?>
                    <div class="popular-badge">সর্বাধিক জনপ্রিয়</div>
                    <?php endif; ?>

                    <div class="pricing-header">
                        <h3 class="pricing-title"><?php echo $plan['name']; ?></h3>
                        <div class="pricing-price">৳<?php echo number_format($plan['price'], 0); ?></div>
                        <div class="pricing-duration">
                            <?php
                            // Calculate months more accurately
                            $days = $plan['duration'];
                            if ($days == 365 || $days == 366) {
                                echo "১২ মাসের জন্য"; // 12 months for 365/366 days
                            } else if ($days >= 30 && $days <= 31) {
                                echo "১ মাসের জন্য"; // 1 month for 30/31 days
                            } else if ($days >= 90 && $days <= 92) {
                                echo "৩ মাসের জন্য"; // 3 months for 90-92 days
                            } else if ($days >= 180 && $days <= 183) {
                                echo "৬ মাসের জন্য"; // 6 months for 180-183 days
                            } else {
                                echo round($days/30) . " মাসের জন্য"; // Otherwise use rounding
                            }
                            ?>
                        </div>
                    </div>

                    <div class="pricing-body">
                        <div class="pricing-features">
                            <?php foreach(explode("\n", $plan['features']) as $feature): ?>
                            <div class="pricing-feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check text-success"></i>
                                </div>
                                <div><?php echo $feature; ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="pricing-footer">
                        <?php if (!isLoggedIn()): ?>
                        <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-danger btn-lg w-100 btn-subscribe">
                            <i class="fas fa-sign-in-alt me-2"></i> লগইন করুন
                        </a>
                        <?php elseif ($is_premium && $current_subscription['plan_id'] == $plan['id']): ?>
                        <button class="btn btn-success btn-lg w-100 btn-subscribe" disabled>
                            <i class="fas fa-check-circle me-2"></i> বর্তমান প্ল্যান
                        </button>
                        <?php else: ?>
                        <a href="<?php echo SITE_URL; ?>/payment.php?plan=<?php echo $plan['id']; ?>" class="btn btn-danger btn-lg w-100 btn-subscribe">
                            <i class="fas fa-crown me-2"></i> সাবস্ক্রাইব করুন
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php
            $current_index++;
            endwhile;
            ?>
        </div>

        <!-- Middle Ad Section -->
        <?php echo renderAdSection('inline', 'Premium Plans Advertisement', '728x90'); ?>

        <!-- Comparison Table (Hidden by Default) -->
        <div class="comparison-table mt-5" id="comparisonTable">
            <div class="table-responsive">
                <table class="table table-dark">
                    <thead>
                        <tr>
                            <th>ফিচার</th>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <th><?php echo $plan['name']; ?></th>
                            <?php endwhile; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Common features to compare -->
                        <tr>
                            <td class="text-start">মূল্য</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <td>৳<?php echo number_format($plan['price'], 0); ?></td>
                            <?php endwhile; ?>
                        </tr>
                        <tr>
                            <td class="text-start">মেয়াদ</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                                $days = $plan['duration'];
                                if ($days == 365 || $days == 366) {
                                    $duration = "১২ মাস";
                                } else if ($days >= 30 && $days <= 31) {
                                    $duration = "১ মাস";
                                } else if ($days >= 90 && $days <= 92) {
                                    $duration = "৩ মাস";
                                } else if ($days >= 180 && $days <= 183) {
                                    $duration = "৬ মাস";
                                } else {
                                    $duration = round($days/30) . " মাস";
                                }
                            ?>
                            <td><?php echo $duration; ?></td>
                            <?php endwhile; ?>
                        </tr>
                        <tr>
                            <td class="text-start">প্রিমিয়াম কন্টেন্ট অ্যাকসেস</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <td><i class="fas fa-check feature-available"></i></td>
                            <?php endwhile; ?>
                        </tr>
                        <tr>
                            <td class="text-start">HD কোয়ালিটি</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            $index = 0;
                            while($plan = mysqli_fetch_assoc($plans_result)):
                                // Assume Basic doesn't have HD, others do
                                $has_hd = ($index > 0);
                            ?>
                            <td>
                                <?php if($has_hd): ?>
                                <i class="fas fa-check feature-available"></i>
                                <?php else: ?>
                                <i class="fas fa-times feature-unavailable"></i>
                                <?php endif; ?>
                            </td>
                            <?php
                            $index++;
                            endwhile;
                            ?>
                        </tr>
                        <tr>
                            <td class="text-start">4K কোয়ালিটি</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            $index = 0;
                            while($plan = mysqli_fetch_assoc($plans_result)):
                                // Assume only Premium has 4K
                                $has_4k = ($index == 2);
                            ?>
                            <td>
                                <?php if($has_4k): ?>
                                <i class="fas fa-check feature-available"></i>
                                <?php else: ?>
                                <i class="fas fa-times feature-unavailable"></i>
                                <?php endif; ?>
                            </td>
                            <?php
                            $index++;
                            endwhile;
                            ?>
                        </tr>
                        <tr>
                            <td class="text-start">একসাথে ডিভাইস</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            $index = 0;
                            while($plan = mysqli_fetch_assoc($plans_result)):
                                // Devices based on plan level
                                $devices = $index == 0 ? "১" : ($index == 1 ? "২" : "৪");
                            ?>
                            <td><?php echo $devices; ?></td>
                            <?php
                            $index++;
                            endwhile;
                            ?>
                        </tr>
                        <tr>
                            <td class="text-start">ডাউনলোড সুবিধা</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <td><i class="fas fa-check feature-available"></i></td>
                            <?php endwhile; ?>
                        </tr>
                        <tr>
                            <td class="text-start">অফলাইন দেখার সুবিধা</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <td><i class="fas fa-check feature-available"></i></td>
                            <?php endwhile; ?>
                        </tr>
                        <tr>
                            <td class="text-start">অ্যাকশন</td>
                            <?php
                            // Reset the result pointer
                            mysqli_data_seek($plans_result, 0);
                            while($plan = mysqli_fetch_assoc($plans_result)):
                            ?>
                            <td>
                                <?php if (!isLoggedIn()): ?>
                                <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-sm btn-outline-danger">লগইন</a>
                                <?php elseif ($is_premium && $current_subscription['plan_id'] == $plan['id']): ?>
                                <button class="btn btn-sm btn-success" disabled>বর্তমান</button>
                                <?php else: ?>
                                <a href="<?php echo SITE_URL; ?>/payment.php?plan=<?php echo $plan['id']; ?>" class="btn btn-sm btn-danger">সাবস্ক্রাইব</a>
                                <?php endif; ?>
                            </td>
                            <?php endwhile; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Additional Benefits -->
        <div class="row mt-5">
            <div class="col-md-4 mb-4">
                <div class="card bg-dark border-0 h-100 text-center p-4">
                    <div class="card-body">
                        <div class="mb-4">
                            <i class="fas fa-ban fa-3x text-danger"></i>
                        </div>
                        <h4 class="text-white">বিজ্ঞাপন মুক্ত</h4>
                        <p class="text-white">প্রিমিয়াম সদস্যরা কোন বিজ্ঞাপন ছাড়াই সম্পূর্ণ কন্টেন্ট উপভোগ করতে পারেন।</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-dark border-0 h-100 text-center p-4">
                    <div class="card-body">
                        <div class="mb-4">
                            <i class="fas fa-download fa-3x text-danger"></i>
                        </div>
                        <h4 class="text-white">ডাউনলোড করুন</h4>
                        <p class="text-white">যেকোনো সময় অফলাইনে দেখার জন্য আপনার পছন্দের কন্টেন্ট ডাউনলোড করুন।</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card bg-dark border-0 h-100 text-center p-4">
                    <div class="card-body">
                        <div class="mb-4">
                            <i class="fas fa-headset fa-3x text-danger"></i>
                        </div>
                        <h4 class="text-white">প্রিমিয়াম সাপোর্ট</h4>
                        <p class="text-white">প্রিমিয়াম সদস্যদের জন্য অগ্রাধিকার সহায়তা এবং সমর্থন।</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Comparison Table Toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('toggleComparison');
    const comparisonTable = document.getElementById('comparisonTable');

    toggleButton.addEventListener('click', function() {
        if (comparisonTable.style.display === 'block') {
            comparisonTable.style.display = 'none';
            toggleButton.innerHTML = '<i class="fas fa-table me-2"></i> প্ল্যান তুলনা দেখুন';
        } else {
            comparisonTable.style.display = 'block';
            toggleButton.innerHTML = '<i class="fas fa-times me-2"></i> তুলনা লুকান';
        }
    });
});
</script>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold">সাধারণ জিজ্ঞাসা</h2>
            <p class="lead text-muted">আপনার প্রশ্নের উত্তর এখানে খুঁজে পেতে পারেন</p>
        </div>

        <div class="accordion" id="faqAccordion">
            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingOne">
                    <button class="accordion-button bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        <i class="fas fa-question-circle me-2 text-danger"></i> প্রিমিয়াম সাবস্ক্রিপশনে কী কী সুবিধা পাওয়া যায়?
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        প্রিমিয়াম সাবস্ক্রিপশন আপনাকে এক্সক্লুসিভ মুভি এবং টিভি শো দেখার সুযোগ দেয়, বিজ্ঞাপন ছাড়া ভিডিও দেখতে পারবেন, কন্টেন্ট ডাউনলোড করে অফলাইনে দেখতে পারবেন, এবং আপনার প্ল্যান অনুযায়ী উচ্চ মানের ভিডিও কোয়ালিটি পাবেন।
                    </div>
                </div>
            </div>

            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingTwo">
                    <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                        <i class="fas fa-times-circle me-2 text-danger"></i> আমি কিভাবে আমার সাবস্ক্রিপশন বাতিল করতে পারি?
                    </button>
                </h2>
                <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        আপনি যেকোনো সময় আপনার অ্যাকাউন্ট সেটিংসে গিয়ে "সাবস্ক্রিপশন বাতিল করুন" বাটনে ক্লিক করে সাবস্ক্রিপশন বাতিল করতে পারেন। আপনার বর্তমান বিলিং পিরিয়ড শেষ না হওয়া পর্যন্ত আপনি প্রিমিয়াম অ্যাকসেস পেতে থাকবেন।
                    </div>
                </div>
            </div>

            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingThree">
                    <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                        <i class="fas fa-money-bill-wave me-2 text-danger"></i> আপনারা কোন কোন পেমেন্ট মেথড গ্রহণ করেন?
                    </button>
                </h2>
                <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        আমরা বিকাশ, নগদ এবং রকেট পেমেন্ট গ্রহণ করি। বিকাশের জন্য আমাদের একটি অটোমেটেড পেমেন্ট সিস্টেম আছে, আর নগদ এবং রকেটের জন্য আমরা ম্যানুয়াল ভেরিফিকেশন সিস্টেম ব্যবহার করি।
                    </div>
                </div>
            </div>

            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingFour">
                    <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                        <i class="fas fa-mobile-alt me-2 text-danger"></i> আমি কি একাধিক ডিভাইসে দেখতে পারব?
                    </button>
                </h2>
                <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        হ্যাঁ, আপনার প্ল্যান অনুযায়ী। বেসিক প্ল্যানে একটি ডিভাইসে একসাথে স্ট্রিমিং করতে পারবেন, স্ট্যান্ডার্ড প্ল্যানে দুটি ডিভাইসে, এবং প্রিমিয়াম প্ল্যানে একসাথে চারটি ডিভাইসে দেখতে পারবেন।
                    </div>
                </div>
            </div>

            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingFive">
                    <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                        <i class="fas fa-gift me-2 text-danger"></i> কি কোন ফ্রি ট্রায়াল আছে?
                    </button>
                </h2>
                <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        বর্তমানে আমরা প্রিমিয়াম সাবস্ক্রিপশনের জন্য কোন ফ্রি ট্রায়াল অফার করি না। তবে আপনি আমাদের প্ল্যাটফর্ম সম্পর্কে ধারণা পেতে সাবস্ক্রিপশন ছাড়াই ফ্রি কন্টেন্ট অ্যাকসেস করতে পারেন।
                    </div>
                </div>
            </div>

            <div class="accordion-item bg-dark text-white border-secondary">
                <h2 class="accordion-header" id="headingSix">
                    <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSix" aria-expanded="false" aria-controls="collapseSix">
                        <i class="fas fa-headset me-2 text-danger"></i> সাপোর্ট কিভাবে পাব?
                    </button>
                </h2>
                <div id="collapseSix" class="accordion-collapse collapse" aria-labelledby="headingSix" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        আপনি আমাদের সাপোর্ট টিমের সাথে যোগাযোগ করতে পারেন <EMAIL> ইমেইলে। প্রিমিয়াম সদস্যদের জন্য আমরা অগ্রাধিকার ভিত্তিতে সাপোর্ট প্রদান করি। আপনি আমাদের <a href="https://t.me/buycinepix" target="_blank" class="text-danger">টেলিগ্রাম গ্রুপে</a> যোগ দিতে পারেন সরাসরি সাহায্য পেতে।
                    </div>
                </div>
            </div>
        </div>

        <!-- Join Telegram Group -->
        <div class="text-center mt-5">
            <a href="https://t.me/buycinepix" target="_blank" class="btn btn-outline-danger btn-lg me-2 mb-2">
                <i class="fab fa-telegram me-2"></i> আমাদের টেলিগ্রাম গ্রুপে যোগ দিন
            </a>
            <a href="#" id="downloadAppBtn" class="btn btn-outline-success btn-lg mb-2">
                <i class="fab fa-android me-2"></i> আমাদের অ্যাপ ডাউনলোড করুন
            </a>
        </div>

        <!-- App Download Modal -->
        <div class="modal fade" id="appDownloadModal" tabindex="-1" aria-labelledby="appDownloadModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content bg-dark text-white">
                    <div class="modal-header border-secondary">
                        <h5 class="modal-title" id="appDownloadModalLabel">সিনেপিক্স অ্যাপ ডাউনলোড করুন</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="assets/images/app_qr.png" alt="App Download QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                        <h4 class="mb-3">সিনেপিক্স অফিসিয়াল অ্যাপ</h4>
                        <p>আমাদের অফিসিয়াল অ্যাপ ডাউনলোড করে আরও সহজে মুভি এবং টিভি সিরিজ দেখুন। অ্যাপে রয়েছে:</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i> অফলাইনে দেখার সুবিধা</li>
                            <li><i class="fas fa-check text-success me-2"></i> মাল্টি-অডিও সাপোর্ট</li>
                            <li><i class="fas fa-check text-success me-2"></i> টিভি রিমোট সাপোর্ট</li>
                            <li><i class="fas fa-check text-success me-2"></i> উন্নত ভিডিও প্লেয়ার</li>
                            <li><i class="fas fa-check text-success me-2"></i> ফাস্ট স্ট্রিমিং</li>
                        </ul>
                        <div class="mt-4">
                            <a href="#" class="btn btn-success btn-lg">
                                <i class="fas fa-download me-2"></i> ডাউনলোড করুন
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- App Download Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const downloadAppBtn = document.getElementById('downloadAppBtn');
                const appDownloadModal = new bootstrap.Modal(document.getElementById('appDownloadModal'));

                downloadAppBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    appDownloadModal.show();
                });
            });
        </script>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
